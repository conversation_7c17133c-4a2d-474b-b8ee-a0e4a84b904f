import { CollectionConfig } from "payload";
import { generateUniversalId, getIdFieldConfig } from '../../hooks/generateUniversalId';


const Events: CollectionConfig = {
  slug: 'events',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'date', 'type', 'location'],
  },
  access: {
    read: () => true,
  },
  fields: [
    getIdFieldConfig('events'),
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: ['keynote', 'panel', 'workshop', 'exhibition', 'breakout'],
    },
    {
      name: 'date',
      type: 'date',
      required: true,
    },
    {
      name: 'startTime',
      type: 'text',
      label: 'Start Time',
    },
    {
      name: 'endTime',
      type: 'text',
      label: 'End Time',
    },
    {
      name: 'day',
      type: 'number',
      label: 'Conference Day',
      required: true,
    },
    {
      name: 'speakers',
      type: 'relationship',
      relationTo: 'speakers',
      hasMany: true,
    },
    {
      name: 'location',
      type: 'text',
    },
    {
      name: 'downloads',
      type: 'upload',
      relationTo: 'media',
      label: 'Supporting Documents',
      required: false,
    },
  ],
  hooks: {
    beforeChange: [generateUniversalId],
  },
};

export default Events;
